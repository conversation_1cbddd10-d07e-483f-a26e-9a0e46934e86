# AiJ 项目开发指南

本文档提供AiJ项目的开发指南，包括代码规范、开发流程和最佳实践。

## 开发环境

- IDE: IntelliJ IDEA、Visual Studio Code、Cursor
- JDK: 8+
- Maven: 3.5+
- Node.js: 12+
- Git: 2.x+

## 代码规范

### Java代码规范

- 缩进使用4个空格
- 行宽不超过120字符
- 类名采用大驼峰命名法(PascalCase)，如`UserService`
- 方法名、变量名采用小驼峰命名法(camelCase)，如`getUserById`
- 常量名采用全大写下划线分隔，如`MAX_RETRY_COUNT`
- 包名全小写，如`com.xiyoufang.aij.core`
- 使用类的完整名称作为导入语句，避免使用通配符导入
- 按以下顺序组织导入语句：
  1. java包
  2. javax包
  3. org包
  4. com包
  5. 静态导入

### JavaScript/TypeScript代码规范

- 缩进使用2个空格
- 行宽不超过100字符
- 使用单引号表示字符串
- 使用分号结束语句
- 类名、接口名、类型别名使用大驼峰命名法
- 方法名、变量名使用小驼峰命名法
- 常量名使用全大写下划线分隔
- 导入语句顺序：
  1. react/vue相关
  2. 第三方库
  3. 项目内模块
  4. 相对路径导入

### Vue组件规范

- 组件名使用大驼峰命名法
- Props、事件名、方法名使用小驼峰命名法
- 模板缩进使用2个空格
- 每个组件对应一个文件
- 组件结构顺序：
  1. template
  2. script
  3. style

## 项目结构

遵循以下项目结构组织代码：

```
aiJ-module/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── xiyoufang/
│   │   │           └── aij/
│   │   │               └── module/
│   │   │                   ├── controller/
│   │   │                   ├── service/
│   │   │                   ├── model/
│   │   │                   ├── dao/
│   │   │                   └── config/
│   │   └── resources/
│   │       ├── config.properties
│   │       └── ...
│   └── test/
│       └── java/
│           └── com/
│               └── xiyoufang/
│                   └── aij/
│                       └── module/
│                           └── ...
└── pom.xml
```

## 开发流程

### 1. 分支管理

- `master`: 主分支，保持稳定，只接受合并请求
- `develop`: 开发分支，最新的开发进度
- `feature/xxx`: 特性分支，用于开发新功能
- `bugfix/xxx`: 修复分支，用于修复bug
- `release/xxx`: 发布分支，用于准备发布

### 2. 提交规范

提交消息格式：

```
<type>(<scope>): <subject>

<body>

<footer>
```

- `type`: 表示提交类型，如feat、fix、docs、style、refactor、test、chore等
- `scope`: 表示影响范围，如auth、room、user等
- `subject`: 简短描述，首字母小写，不超过50个字符
- `body`: 详细描述
- `footer`: 关闭issue或注释

例如：

```
feat(room): 添加房间邀请功能

添加了玩家邀请好友进入房间的功能，包括：
1. 生成邀请链接
2. 分享邀请链接
3. 通过邀请链接加入房间

Closes #123
```

### 3. 开发步骤

1. 从develop分支创建特性分支
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/xxx
   ```

2. 开发新功能

3. 提交更改
   ```bash
   git add .
   git commit -m "feat(xxx): 描述"
   ```

4. 同步develop分支
   ```bash
   git checkout develop
   git pull
   git checkout feature/xxx
   git merge develop
   ```

5. 解决冲突并提交

6. 推送到远程
   ```bash
   git push origin feature/xxx
   ```

7. 创建合并请求（Pull Request）

### 4. 代码审查

- 每个合并请求需要至少一名团队成员审查
- 审查重点：代码质量、性能、安全性、可读性、测试覆盖
- 使用评论功能讨论代码问题
- 解决所有评论后再合并

## 测试规范

### 单元测试

- 使用JUnit 4/5编写单元测试
- 每个测试方法测试一个功能点
- 测试方法名称应描述被测试的行为
- 使用Mockito模拟依赖
- 测试覆盖率目标：80%+

### 集成测试

- 使用Spring Test或TestContainers进行集成测试
- 测试API端点和数据库交互
- 使用测试配置文件
- 使用内存数据库进行测试

## 调试技巧

### 服务端调试

1. 远程调试
   ```bash
   java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar xxx.jar
   ```

2. 日志调试
   ```java
   logger.debug("变量值: {}", value);
   ```

### 客户端调试

1. 使用Chrome DevTools
2. 使用CocosCreator内置调试工具
3. 添加断点和日志

## 扩展开发指南

### 1. 开发新游戏模块

1. 创建新模块项目（如aiJ-poker）
2. 实现游戏规则和逻辑
3. 实现游戏AI
4. 集成到aiJ-Room服务
5. 开发客户端界面

### 2. 开发亲友圈功能扩展

1. 在aiJ-Circle模块中添加新功能
2. 实现数据模型和服务
3. 实现API和消息处理
4. 更新客户端界面

### 3. 优化游戏性能

1. 使用性能分析工具定位瓶颈
2. 优化数据结构和算法
3. 添加缓存
4. 优化网络传输
5. 优化数据库查询

## 常见问题及解决方案

### 1. 服务注册失败

- 检查ZooKeeper连接
- 检查网络配置
- 查看日志详细信息

### 2. WebSocket连接断开

- 实现心跳检测
- 添加自动重连机制
- 优化网络配置

### 3. 游戏同步问题

- 使用帧同步机制
- 添加状态验证
- 记录和回放操作序列

## 文档和注释

- 为公共API添加JavaDoc注释
- 为复杂逻辑添加代码注释
- 更新README和开发文档
- 添加架构和流程图 