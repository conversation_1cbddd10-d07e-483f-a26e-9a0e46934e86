# 仰牌测试恢复说明

## 测试完成后的恢复步骤

### 1. 恢复正常发牌逻辑

将 `aiJ-mahjong/src/main/java/com/xiyoufang/aij/mahjong/MahjongTableAbility.java` 文件中的 `onGameReset()` 方法恢复为：

```java
@Override
public void onGameReset() {
    repertoryCard = MahjongKit.shuffle(MahjongConst.CARDS, 2);
    leftCardCount = repertoryCard.length;
    cardIndices = new byte[chairCount][];
    currCard = INVALID_CARD;
    currChair = Table.INVALID_CHAIR;
    outCardChair = Table.INVALID_CHAIR;
    resumeChair = Table.INVALID_CHAIR;
    dispatchCardNumber = 0;
    diceSum = 0;
    discardCount = new int[chairCount];
    discardCards = new byte[chairCount][MahjongConst.CARDS.length];
    heroWeaveItems = new ConcurrentHashMap<Integer, List<WeaveItem>>();
    hHeroes = new ConcurrentHashMap<>();
    action = new int[chairCount];
    performAction = new int[chairCount];
    response = new boolean[chairCount];
    actionCard = new byte[chairCount];
    actionCards = new byte[chairCount][];
    Arrays.fill(actionCards, new byte[]{});
    grabG = false;
    gOpen = false;
    //游戏记录
    mahjongGameRecord = new MahjongGameRecord();
    mahjongGameRecord.setRepertory(repertoryCard);
    mahjongRecord.getMahjongGameRecords().add(mahjongGameRecord);
}
```

### 2. 具体修改位置

找到第327-339行的代码：

```java
// 临时测试：指定发牌顺序，确保第一个玩家能拿到中发白三张牌
repertoryCard = MahjongKit.specifyShuffle(new byte[]{
    // ... 指定的牌序列
});
// repertoryCard = MahjongKit.shuffle(MahjongConst.CARDS, 2);
```

替换为：

```java
repertoryCard = MahjongKit.shuffle(MahjongConst.CARDS, 2);
```

### 3. 验证恢复

恢复后重新启动游戏，确认：
- 发牌是随机的
- 不会每次都有仰操作按钮
- 游戏正常进行

## 注意事项

- 只需要修改一个地方：`onGameReset()` 方法中的发牌逻辑
- 其他仰操作的功能代码保持不变
- 恢复后仰操作功能仍然可用，只是不会每次都触发
