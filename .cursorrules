{"version": "1.0", "rules": {"java": {"style": {"indent": 4, "maxLineLength": 120, "braceStyle": "end-of-line"}, "naming": {"class": "^[A-Z][a-zA-Z0-9]*$", "interface": "^[A-Z][a-zA-Z0-9]*$", "enum": "^[A-Z][a-zA-Z0-9]*$", "method": "^[a-z][a-zA-Z0-9]*$", "variable": "^[a-z][a-zA-Z0-9]*$", "constant": "^[A-Z][A-Z0-9_]*$", "parameter": "^[a-z][a-zA-Z0-9]*$"}, "imports": {"order": ["java", "javax", "org", "com", "static"], "separatedByBlankLine": true, "noWildcardImports": true, "noUnusedImports": true}}, "typescript": {"style": {"indent": 2, "maxLineLength": 100, "semicolons": true, "quotes": "single", "trailingComma": "es5", "bracketSpacing": true, "arrowParens": "avoid"}, "naming": {"interface": "^[A-Z][a-zA-Z0-9]*$", "typeAlias": "^[A-Z][a-zA-Z0-9]*$", "enum": "^[A-Z][a-zA-Z0-9]*$", "function": "^[a-z][a-zA-Z0-9]*$", "variable": "^[a-z][a-zA-Z0-9]*$", "parameter": "^[a-z][a-zA-Z0-9]*$", "constant": "^[A-Z][A-Z0-9_]*$"}, "imports": {"order": ["react", "vue", "@types", "@", ".", ".."], "separatedByBlankLine": true, "noUnusedImports": true}}, "vue": {"style": {"indent": 2, "maxLineLength": 100, "semicolons": true, "quotes": "single", "trailingComma": "es5", "bracketSpacing": true, "arrowParens": "avoid"}, "component": {"name": "^[A-Z][a-zA-Z0-9]*$", "props": "^[a-z][a-zA-Z0-9]*$", "events": "^[a-z][a-zA-Z0-9]*$", "methods": "^[a-z][a-zA-Z0-9]*$", "computed": "^[a-z][a-zA-Z0-9]*$", "data": "^[a-z][a-zA-Z0-9]*$"}, "template": {"indent": 2, "maxLineLength": 100, "selfClosing": true, "htmlWhitespaceSensitivity": "css"}}, "directory": {"structure": {"aiJ-client": {"src": {"assets": {}, "components": {}, "views": {}, "router": {}, "store": {}, "utils": {}, "api": {}}}, "aiJ-core": {"src/main/java/com/xiyoufang/aij": {"cache": {}, "core": {}, "event": {}, "handler": {}, "response": {}, "timer": {}, "user": {}, "utils": {}}}, "aiJ-platform": {"src/main/java/com/xiyoufang/aij/platform": {"controller": {}, "service": {}, "model": {}, "dao": {}, "config": {}}}, "aiJ-view": {"src": {"api": {}, "assets": {}, "components": {}, "layout": {}, "router": {}, "store": {}, "utils": {}, "views": {}}}}}}}