# aiJ 客户端

基于 Cocos Creator 和 FairyGUI 开发的房间类游戏客户端。

## 项目结构

- `assets/Scene`: 游戏场景文件
- `assets/Script`: 游戏脚本
  - `ws/`: WebSocket 通信相关
  - `ui/`: UI 管理相关
  - `game/`: 游戏逻辑相关
- `assets/resources`: 游戏资源
- `assets/FairyGUI`: FairyGUI UI 包

## 开发环境

- Cocos Creator: 2.1.x
- FairyGUI: 3.x
- Node.js: 10.x+

## 运行项目

1. 使用 Cocos Creator 2.1.x 打开项目
2. 确保服务端已启动
3. 点击编辑器中的"运行"按钮

## 常见问题

- 网络连接问题
- UI 资源加载问题
- 性能优化

# 配置
Hello world new project template.

## 在main.js中添加如下代码，将热更新下载的资源目录加载到Search目录
```js
    var hotUpdateSearchPaths = localStorage.getItem('HotUpdateSearchPaths');
    if (hotUpdateSearchPaths) {
        jsb.fileUtils.setSearchPaths(JSON.parse(hotUpdateSearchPaths));
    }
```
## 创建版本信息文件
打包前执行如下脚本
```shell script
node version_generator.js -v 1.0.0 -u http://localhost/tutorial-hot-update/remote-assets/ -s build/jsb-link/ -d assets/
```

# 麻将游戏项目架构

## 1. UI层 (MahjongGameLayer)
- 游戏界面渲染和用户交互
- 东南西北四个方向视图
- 弃牌区、操作提示、头像等UI元素
- 按钮点击等用户输入处理

### 1.1 视图组件
- South View (南家视图)
- East View (东家视图)
- North View (北家视图)
- West View (西家视图)
- Discard Views (弃牌区视图)
- Operate Notify View (操作提示视图)
- Head Views (头像视图)
- Game Over Views (游戏结束视图)
- End Views (结算视图)

## 2. 游戏引擎 (MahjongGameEngine)
- 核心游戏逻辑处理
- 游戏状态管理
- 玩家管理
- 牌局和操作管理

### 2.1 核心功能
- 游戏状态控制
- 玩家信息管理
- 牌局管理
- 操作管理

## 3. 事件系统
### 3.1 游戏事件
- 游戏开始
- 游戏结束
- 游戏状态更新

### 3.2 玩家事件
- 玩家进入
- 玩家离开
- 在线状态变更
- 坐下/起立

### 3.3 牌局事件
- 发牌
- 出牌
- 摸牌

### 3.4 操作事件
- 吃
- 碰
- 杠
- 胡

## 4. 响应系统
### 4.1 游戏响应
- 游戏状态响应
- 游戏场景响应
- 游戏结束响应

### 4.2 玩家响应
- 玩家信息响应
- 玩家状态响应
- 玩家操作响应

### 4.3 牌局响应
- 发牌响应
- 出牌响应
- 操作响应

## 5. 数据结构
### 5.1 牌组合结构 (MahjongWeaveItem)
- 牌组合类型
- 牌组合数据

### 5.2 牌组合类型 (MahjongWeaveType)
- 吃
- 碰
- 杠
- 胡

### 5.3 玩家信息结构 (HeroMate)
- 座位信息
- 在线状态
- 坐下状态
- 昵称

## 6. 主要流程
### 6.1 游戏初始化
1. MahjongGameLayer加载UI资源
2. 创建MahjongGameEngine实例
3. 注册事件监听

### 6.2 游戏进行
1. 引擎接收服务器事件
2. 处理游戏逻辑
3. 更新界面显示

### 6.3 玩家操作
1. UI层接收用户输入
2. 发送操作事件到服务器
3. 等待服务器响应
4. 更新游戏状态和界面

### 6.4 游戏结束
1. 处理结算信息
2. 显示游戏结果
3. 准备下一局

## 7. 架构特点
- 采用MVC架构模式
- 逻辑与显示分离
- 事件驱动设计
- 模块化结构
- 易于维护和扩展

