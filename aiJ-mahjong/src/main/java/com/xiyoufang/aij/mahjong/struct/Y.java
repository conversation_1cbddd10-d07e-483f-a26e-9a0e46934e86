package com.xiyoufang.aij.mahjong.struct;

/**
 * Yang操作结构
 * Yang操作特征：由三张"中发白"牌组成
 *
 * <AUTHOR>
 */
public class Y {
    /**
     * 玩家椅子号
     */
    private int chair;
    /**
     * Yang操作的牌（中发白三张牌）
     */
    private byte[] cards;

    /**
     * 构造器
     * 
     * @param chair 玩家椅子号
     * @param cards Yang操作的牌（中发白三张牌）
     */
    public Y(int chair, byte[] cards) {
        this.chair = chair;
        this.cards = cards;
    }

    public int getChair() {
        return chair;
    }

    public void setChair(int chair) {
        this.chair = chair;
    }

    public byte[] getCards() {
        return cards;
    }

    public void setCards(byte[] cards) {
        this.cards = cards;
    }
}
