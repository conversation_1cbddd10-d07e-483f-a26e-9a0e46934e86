package com.xiyoufang.aij.mahjong.response;

import com.xiyoufang.aij.response.CommonResponse;

/**
 * Created by 席有芳 on 2018-12-23.
 *
 * <AUTHOR>
 */
public class OperateNotifyEventResponse extends CommonResponse {
    /**
     * 动作
     */
    private int action;
    /**
     * 被通知人
     */
    private int chair;
    /**
     * 供应人
     */
    private int provider;
    /**
     * 对应的牌
     */
    private byte card;
    /**
     * 杠的牌
     */
    private byte[] cards;
    /**
     * 吃牌的组合
     */
    private byte[][] combinations;
    /**
     * 单个组合（用于仰操作等）
     */
    private byte[] combination;

    public int getProvider() {
        return provider;
    }

    public void setProvider(int provider) {
        this.provider = provider;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public int getChair() {
        return chair;
    }

    public void setChair(int chair) {
        this.chair = chair;
    }

    public byte getCard() {
        return card;
    }

    public void setCard(byte card) {
        this.card = card;
    }

    public byte[] getCards() {
        return cards;
    }

    public void setCards(byte[] cards) {
        this.cards = cards;
    }
    
    public byte[][] getCombinations() {
        return combinations;
    }

    public void setCombinations(byte[][] combinations) {
        this.combinations = combinations;
    }

    public byte[] getCombination() {
        return combination;
    }

    public void setCombination(byte[] combination) {
        this.combination = combination;
    }
}
