package com.xiyoufang.aij.mahjong.struct;

/**
 * Created by 席有芳 on 2018/1/21.
 * 面牌组合类型
 *
 * <AUTHOR>
 */
public enum WeaveType {

    C((byte) 1, "吃"),   //吃
    P((byte) 2, "碰"),   //碰
    G((byte) 3, "杠"),    //杠
    Y((byte) 4, "仰");    //仰

    private byte value;
    private String name;

    WeaveType(byte value, String name) {
        this.value = value;
        this.name = name;
    }

    public byte getValue() {
        return value;
    }

    public void setValue(byte value) {
        this.value = value;
    }
    
    public String getName() {
        return name;
    }
}
