package com.xiyoufang.aij.mahjong;

import com.xiyoufang.aij.mahjong.rule.*;
import com.xiyoufang.aij.mahjong.struct.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by 席有芳 on 2018-12-21.
 *
 * <AUTHOR>
 */
public class MahjongLogic {

    private List<HRule> rules = new ArrayList<>();

    /**
     * 麻将逻辑
     */
    public MahjongLogic() {
        rules.add(new PingHRule());
        rules.add(new SevenPairHRule());
        rules.add(new SameColourHRule());
        rules.add(new JiangYiseHRule());
        rules.add(new PengPengHRule());
    }

    /**
     * 碰牌判定
     *
     * @param indices indices
     * @param card    card
     * @param chair   供应的椅子
     * @return 碰牌列表
     */
    public P estimateP(byte[] indices, byte card, int chair) {
        if (indices[MahjongKit.switchCardToIndex(card)] >= 2) {
            return new P(chair, card);
        }
        return null;
    }

    /**
     * 吃牌判定
     * 
     * @param indices indices
     * @param card    当前牌
     * @param chair   供应的椅子
     * @return 吃牌对象
     */
    public C estimateC(byte[] indices, byte card, int chair) {
        // 吃牌只能是万、筒、条这三种花色的牌，不能是风和字牌
        byte cardColor = (byte) (card & MahjongConst.MASK_COLOR);
        if (cardColor == 0x30) { // 风和字牌
            return null;
        }

        byte cardValue = (byte) (card & MahjongConst.MASK_VALUE);

        // 牌值必须在1-9之间
        if (cardValue < 1 || cardValue > 9) {
            return null;
        }

        C c = new C(chair, card);
        boolean hasCombination = false;

        // 检查三种可能的顺子组合：
        // 1. ABC型：当前牌作为第一张（如123中的1）
        // 2. ABC型：当前牌作为第二张（如123中的2）
        // 3. ABC型：当前牌作为第三张（如123中的3）

        // 情况1：当前牌作为顺子的第一张（如123中的1）
        if (cardValue <= 7) { // 最大只能是7，因为789是最大的顺子
            byte nextCard1 = (byte) (cardColor | (cardValue + 1));
            byte nextCard2 = (byte) (cardColor | (cardValue + 2));
            if (indices[MahjongKit.switchCardToIndex(nextCard1)] > 0 &&
                indices[MahjongKit.switchCardToIndex(nextCard2)] > 0) {
                byte[] combination = {card, nextCard1, nextCard2};
                c.addCombination(combination);
                hasCombination = true;
            }
        }

        // 情况2：当前牌作为顺子的第二张（如123中的2）
        if (cardValue >= 2 && cardValue <= 8) { // 2-8之间才能作为中间牌
            byte prevCard = (byte) (cardColor | (cardValue - 1));
            byte nextCard = (byte) (cardColor | (cardValue + 1));
            if (indices[MahjongKit.switchCardToIndex(prevCard)] > 0 &&
                indices[MahjongKit.switchCardToIndex(nextCard)] > 0) {
                byte[] combination = {prevCard, card, nextCard};
                c.addCombination(combination);
                hasCombination = true;
            }
        }

        // 情况3：当前牌作为顺子的第三张（如123中的3）
        if (cardValue >= 3) { // 最小只能是3，因为123是最小的顺子
            byte prevCard1 = (byte) (cardColor | (cardValue - 2));
            byte prevCard2 = (byte) (cardColor | (cardValue - 1));
            if (indices[MahjongKit.switchCardToIndex(prevCard1)] > 0 &&
                indices[MahjongKit.switchCardToIndex(prevCard2)] > 0) {
                byte[] combination = {prevCard1, prevCard2, card};
                c.addCombination(combination);
                hasCombination = true;
            }
        }

        return hasCombination ? c : null;
    }

    /**
     * 杠牌判定
     *
     * @param indices    indices
     * @param card       当前牌
     * @param weaveItems 组合
     * @param self       自己供应
     * @param chair      供应者
     * @return 存在杠的牌列表
     */
    public G estimateG(byte[] indices, byte card, List<WeaveItem> weaveItems, boolean self, int chair) {
        List<G._G> gs = new ArrayList<>();
        if (!self) {
            if (indices[MahjongKit.switchCardToIndex(card)] == 3) {//明杠
                return new G(Collections.singletonList(new G._G(G.M, chair, card)));
            }
        } else {
            for (byte i = 0; i < indices.length; i++) {
                if (indices[i] == 4) {  //暗杠
                    gs.add(new G._G(G.A, chair, MahjongKit.switchIndexToCard(i)));
                }
            }
            for (WeaveItem weaveItem : weaveItems) {
                if (weaveItem.getWeaveType() == WeaveType.P && weaveItem.getCenterCard() == card) {   //拐弯杠
                    gs.add(new G._G(G.G, chair, card));
                }
            }
            for (WeaveItem weaveItem : weaveItems) {
                if (weaveItem.getWeaveType() == WeaveType.P && weaveItem.getCenterCard() != card) {   //拐弯杠后杠
                    if (indices[MahjongKit.switchCardToIndex(weaveItem.getCenterCard())] == 1) {
                        gs.add(new G._G(G.H, chair, weaveItem.getCenterCard()));
                    }
                }
            }
        }
        if (gs.size() > 0) {
            return new G(gs);
        }
        return null;
    }

    /**
     * 胡牌判断
     *
     * @param indices            手上的牌
     * @param card               当前牌
     * @param weaveItems         面牌组合
     * @param self               是否自己摸到牌
     * @param chair              供应者
     * @param source             检测类型
     * @param dispatchCardNumber 派发牌的数量
     * @return 胡牌类型列表
     */
    public H estimateH(byte[] indices, byte card, List<WeaveItem> weaveItems, boolean self, int chair, Source source, int dispatchCardNumber) {
        // 严格检查参数
        if (indices == null || card < 0 || (weaveItems == null && !self)) {
            return null;
        }
        
        byte[] copyCardIndices = Arrays.copyOf(indices, indices.length);
        if (!self) {
            copyCardIndices[MahjongKit.switchCardToIndex(card)] += 1;
        }
        
        // 检查总牌数是否符合胡牌条件
        int totalCards = MahjongKit.getCardNumber(copyCardIndices);
        int weaveCardsCount = 0;
        int gangCount = 0;  // 记录杠的数量

        if (weaveItems != null) {
            for (WeaveItem item : weaveItems) {
                if (item.getWeaveType() == WeaveType.P) {
                    weaveCardsCount += 3;
                } else if (item.getWeaveType() == WeaveType.G) {
                    weaveCardsCount += 4;
                    if (item.isValid()) {  // 只计算有效的杠
                        gangCount++;
                    }
                } else if (item.getWeaveType() == WeaveType.C) {
                    weaveCardsCount += 3;
                }
            }
        }

        // 允许总牌数为14或14+杠的数量
        if (totalCards + weaveCardsCount != 14 && totalCards + weaveCardsCount != 14 + gangCount) {
            System.out.println(chair + "玩家牌数校验不通过,无法胡牌：" +totalCards + weaveCardsCount + "或" + totalCards + weaveCardsCount);
            return null;
        }
        
        List<H._H> hs = new ArrayList<>();
        for (HRule hRule : rules) {
            H._H h = hRule.run(chair, Arrays.copyOf(copyCardIndices, copyCardIndices.length), card, weaveItems, self, source);
            if (h != null) {
                hs.add(h);
            }
        }
        
        if (hs.size() > 0) {
            return new H(hs);
        }
        return null;
    }

    /**
     * 仰操作判定
     * 仰操作特征：由三张"中发白"牌组成
     *
     * @param indices 手牌索引
     * @param chair   玩家椅子号
     * @return 仰操作结构，如果不能仰则返回null
     */
    public Y estimateY(byte[] indices, int chair) {
        // 中发白的牌值：中(0x35)、发(0x36)、白(0x37)
        byte zhong = (byte) 0x35; // 中
        byte fa = (byte) 0x36;    // 发
        byte bai = (byte) 0x37;   // 白

        // 检查手牌中是否有中发白各一张
        byte zhongIndex = MahjongKit.switchCardToIndex(zhong);
        byte faIndex = MahjongKit.switchCardToIndex(fa);
        byte baiIndex = MahjongKit.switchCardToIndex(bai);

        // 添加调试日志
        System.out.println("=== 仰操作检查 ===");
        System.out.println("玩家椅子: " + chair);
        System.out.println("中(0x35)牌值转索引: " + zhongIndex + ", 手牌中数量: " + (zhongIndex < indices.length ? indices[zhongIndex] : "索引越界"));
        System.out.println("发(0x36)牌值转索引: " + faIndex + ", 手牌中数量: " + (faIndex < indices.length ? indices[faIndex] : "索引越界"));
        System.out.println("白(0x37)牌值转索引: " + baiIndex + ", 手牌中数量: " + (baiIndex < indices.length ? indices[baiIndex] : "索引越界"));

        // 打印所有手牌索引
        System.out.print("手牌索引数组: ");
        for (int i = 0; i < indices.length; i++) {
            if (indices[i] > 0) {
                byte card = MahjongKit.switchIndexToCard((byte)i);
                System.out.print("索引" + i + "(牌值0x" + Integer.toHexString(card & 0xFF).toUpperCase() + ")=" + indices[i] + " ");
            }
        }
        System.out.println();

        // 检查索引是否有效
        if (zhongIndex >= indices.length || faIndex >= indices.length || baiIndex >= indices.length) {
            System.out.println("仰操作条件不满足：索引越界");
            return null;
        }

        if (indices[zhongIndex] >= 1 && indices[faIndex] >= 1 && indices[baiIndex] >= 1) {
            System.out.println("仰操作条件满足！");
            // 可以进行仰操作，返回中发白三张牌
            byte[] cards = {zhong, fa, bai};
            return new Y(chair, cards);
        } else {
            System.out.println("仰操作条件不满足：中=" + indices[zhongIndex] + ", 发=" + indices[faIndex] + ", 白=" + indices[baiIndex]);
        }

        return null;
    }

}
