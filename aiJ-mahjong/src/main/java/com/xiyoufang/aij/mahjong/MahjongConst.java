package com.xiyoufang.aij.mahjong;

/**
 * Created by 席有芳 on 2018-12-21.
 *
 * <AUTHOR>
 */
public class MahjongConst {
    /**
     * 牌类型
     */
    public final static byte MASK_COLOR = (byte) 0xF0;
    /**
     * 牌的值
     */
    public final static byte MASK_VALUE = (byte) 0x0F;
    /**
     * 最大索引
     */
    public final static int MAX_INDEX = 34;
    /**
     * 单人手上最多
     */
    public final static int MAX_COUNT = 14;
    /**
     * 什么都不操作
     */
    public final static int N = 0;
    /**
     * 碰的Action标记位置
     */
    public final static int P = 1;
    /**
     * 杠的Acton标记位置
     */
    public final static int G = 2;
    /**
     * 胡的Action标记位置
     */
    public final static int H = 4;
    /**
     * 吃的Action标记位置
     */
    public final static int C = 8;
    /**
     * 仰的Action标记位置
     */
    public final static int Y = 16;
    /**
     * 136张
     */
    public final static byte[] CARDS = new byte[]{
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,                        //筒子
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,                        //筒子
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,                        //筒子
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,                        //筒子
            0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,                        //万子
            0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,                        //万子
            0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,                        //万子
            0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,                        //万子
            0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,                        //条子
            0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,                        //条子
            0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,                        //条子
            0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,                        //条子
            0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,                                    //番子
            0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,                                    //番子
            0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,                                    //番子
            0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,                                    //番子
    };

}
