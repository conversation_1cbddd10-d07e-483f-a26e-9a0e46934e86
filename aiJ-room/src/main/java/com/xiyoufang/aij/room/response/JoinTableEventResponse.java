package com.xiyoufang.aij.room.response;

import com.xiyoufang.aij.response.CommonResponse;

/**
 * Created by 席有芳 on 2018-12-20.
 *
 * <AUTHOR>
 */
public class JoinTableEventResponse extends CommonResponse {
    /**
     * 显示用的ID
     */
    private String showId;
    /**
     * self id
     */
    private String userId;
    /**
     * 桌子编号
     */
    private int tableNo;
    /**
     * 椅子
     */
    private int chair;
    /**
     * 规则
     */
    private String ruleText;
    /**
     * owner user id
     */
    private String ownerId;

    public String getShowId() {
        return showId;
    }

    public void setShowId(String showId) {
        this.showId = showId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getTableNo() {
        return tableNo;
    }

    public void setTableNo(int tableNo) {
        this.tableNo = tableNo;
    }

    public int getChair() {
        return chair;
    }

    public void setChair(int chair) {
        this.chair = chair;
    }

    public String getRuleText() {
        return ruleText;
    }

    public void setRuleText(String ruleText) {
        this.ruleText = ruleText;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerId() {
        return ownerId;
    }
}
